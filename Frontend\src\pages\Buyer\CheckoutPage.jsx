import React, { useState, useEffect, useRef } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { getOrder } from "../../redux/slices/orderSlice";
import StripePaymentForm from "../../components/payment/StripePaymentForm";
import LoadingSkeleton from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import { toast } from "react-toastify";
import { VALIDATION, IMAGE_BASE_URL } from "../../utils/constants";
import { formatStandardDate } from "../../utils/dateValidation";
import "../../styles/CheckoutPage.css";

const CheckoutPage = () => {
  const { orderId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const autocompleteRef = useRef(null);
  const inputRef = useRef(null);

  const { user } = useSelector((state) => state.auth);
  const { isLoading, error } = useSelector((state) => state.order);

  const [paymentStep, setPaymentStep] = useState("loading"); // loading, payment, success, error
  const [currentOrder, setCurrentOrder] = useState(null); // Local state for current order
  const [address, setAddress] = useState(null); // Store selected address
  const [taxCalculation, setTaxCalculation] = useState(null); // Store Stripe tax calculation
  const [isAddressLoading, setIsAddressLoading] = useState(false);

  // Load Google Maps Places API
  useEffect(() => {
    const script = document.createElement("script");
    script.src = `https://maps.googleapis.com/maps/api/js?key=YOUR_GOOGLE_MAPS_API_KEY&libraries=places`;
    script.async = true;
    script.onload = () => {
      if (window.google && window.google.maps && inputRef.current) {
        autocompleteRef.current = new window.google.maps.places.Autocomplete(
          inputRef.current,
          {
            types: ["address"],
            componentRestrictions: { country: "us" }, // Restrict to US addresses
            fields: ["address_components", "formatted_address"],
          }
        );

        autocompleteRef.current.addListener("place_changed", () => {
          const place = autocompleteRef.current.getPlace();
          if (place.address_components) {
            const addressComponents = place.address_components;
            const formattedAddress = {
              street: "",
              city: "",
              state: "",
              postalCode: "",
              country: "",
            };

            addressComponents.forEach((component) => {
              const types = component.types;
              if (types.includes("street_number")) {
                formattedAddress.street = component.long_name;
              }
              if (types.includes("route")) {
                formattedAddress.street += ` ${component.long_name}`;
              }
              if (types.includes("locality")) {
                formattedAddress.city = component.long_name;
              }
              if (types.includes("administrative_area_level_1")) {
                formattedAddress.state = component.short_name;
              }
              if (types.includes("postal_code")) {
                formattedAddress.postalCode = component.long_name;
              }
              if (types.includes("country")) {
                formattedAddress.country = component.short_name;
              }
            });

            setAddress(formattedAddress);
            calculateTax(formattedAddress);
          }
        });
      }
    };
    document.head.appendChild(script);

    return () => {
      document.head.removeChild(script);
    };
  }, []);

  // Fetch order details and check user permissions
  useEffect(() => {
    if (!user) {
      toast.error("Please log in to complete your purchase");
      navigate("/login");
      return;
    }

    const effectiveRole =
      user.role === "admin" ? user.role : user.activeRole || user.role;
    if (effectiveRole !== "buyer" && user.role !== "admin") {
      toast.error("Only buyers can make purchases");
      navigate("/");
      return;
    }

    if (!VALIDATION.isValidId(orderId)) {
      console.error("Invalid order ID:", orderId);
      toast.error("Invalid order ID. Please try creating a new order.");
      navigate("/buyer/dashboard");
      return;
    }

    dispatch(getOrder(orderId))
      .unwrap()
      .then((result) => {
        setCurrentOrder(result.data);
        setPaymentStep("payment");
      })
      .catch((err) => {
        console.error("Error fetching order:", err);
        toast.error("Order not found or you do not have permission to view it");
        navigate("/buyer/dashboard");
      });
  }, [dispatch, orderId, user, navigate]);

  // Calculate tax using Stripe Tax API
  const calculateTax = async (address) => {
    if (!address || !currentOrder) return;

    setIsAddressLoading(true);
    try {
      const response = await fetch("/api/stripe/calculate-tax", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          orderId: currentOrder._id,
          amount: currentOrder.amount * 100, // Convert to cents
          currency: "usd",
          customerDetails: {
            address: {
              line1: address.street,
              city: address.city,
              state: address.state,
              postal_code: address.postalCode,
              country: address.country,
            },
          },
        }),
      });

      const taxData = await response.json();
      if (taxData.taxCalculation) {
        setTaxCalculation(taxData.taxCalculation);
      } else {
        toast.error("Unable to calculate tax. Please try again.");
      }
    } catch (error) {
      console.error("Error calculating tax:", error);
      toast.error("Failed to calculate tax. Please try again.");
    } finally {
      setIsAddressLoading(false);
    }
  };

  const handlePaymentSuccess = async (paymentResult) => {
    toast.success("Payment completed successfully!");
    setPaymentStep("success");

    try {
      const updatedOrderResult = await dispatch(
        getOrder(currentOrder._id)
      ).unwrap();
      const updatedOrder = updatedOrderResult.data;

      const getCardTypeDisplayName = (cardType) => {
        const cardTypeNames = {
          visa: "Visa",
          mastercard: "Mastercard",
          amex: "American Express",
          discover: "Discover",
          diners: "Diners Club",
          jcb: "JCB",
          unionpay: "UnionPay",
          unknown: "Card",
        };
        return cardTypeNames[cardType?.toLowerCase()] || "Card Payment";
      };

      const formatCardNumber = (lastFourDigits) => {
        if (lastFourDigits) {
          return `**** **** **** ${lastFourDigits}`;
        }
        return "**** **** **** ****";
      };

      const orderData = {
        orderId: `#${updatedOrder._id?.slice(-8) || "12345678"}`,
        date: formatStandardDate(updatedOrder.createdAt || Date.now()),
        time: new Date(updatedOrder.createdAt || Date.now()).toLocaleTimeString(
          "en-US",
          {
            hour: "numeric",
            minute: "2-digit",
            hour12: true,
          }
        ),
        items: 1,
        totalAmount: `$${(
          (updatedOrder.amount || 0) +
          (taxCalculation?.amount_total / 100 || 0)
        ).toFixed(2)}`,
        customerDetails: {
          name:
            updatedOrder.buyer?.firstName && updatedOrder.buyer?.lastName
              ? `${updatedOrder.buyer.firstName} ${updatedOrder.buyer.lastName}`
              : updatedOrder.buyer?.name || "Customer",
          email: updatedOrder.buyer?.email || "<EMAIL>",
          phone:
            updatedOrder.buyer?.mobile ||
            updatedOrder.buyer?.phone ||
            "Not provided",
          address: address || { street: "Not provided" },
        },
        paymentDetails: {
          method: getCardTypeDisplayName(updatedOrder.cardDetails?.cardType),
          cardNumber: formatCardNumber(
            updatedOrder.cardDetails?.lastFourDigits
          ),
          cardType: updatedOrder.cardDetails?.cardType || "unknown",
        },
        itemInfo: {
          title: updatedOrder.content?.title || "Digital Content",
          category:
            updatedOrder.content?.category ||
            updatedOrder.content?.sport ||
            "Sports Content",
          image:
            updatedOrder.content?.thumbnail ||
            updatedOrder.content?.thumbnailUrl ||
            "https://via.placeholder.com/80x80/f0f0f0/666666?text=Content",
        },
        fullOrder: updatedOrder,
        paymentResult: paymentResult,
        taxAmount: taxCalculation ? `$${(taxCalculation.amount_total / 100).toFixed(2)}` : "$0.00",
      };

      setTimeout(() => {
        navigate("/thank-you", {
          state: { orderData },
        });
      }, 2000);
    } catch (error) {
      console.error("Error fetching updated order:", error);
      const orderData = {
        orderId: `#${currentOrder._id?.slice(-8) || "12345678"}`,
        date: formatStandardDate(currentOrder.createdAt || Date.now()),
        time: new Date(currentOrder.createdAt || Date.now()).toLocaleTimeString(
          "en-US",
          {
            hour: "numeric",
            minute: "2-digit",
            hour12: true,
          }
        ),
        items: 1,
        totalAmount: `$${(
          (currentOrder.amount || 0) +
          (taxCalculation?.amount_total / 100 || 0)
        ).toFixed(2)}`,
        customerDetails: {
          name:
            currentOrder.buyer?.firstName && currentOrder.buyer?.lastName
              ? `${currentOrder.buyer.firstName} ${currentOrder.buyer.lastName}`
              : currentOrder.buyer?.name || "Customer",
          email: currentOrder.buyer?.email || "<EMAIL>",
          phone:
            currentOrder.buyer?.mobile ||
            currentOrder.buyer?.phone ||
            "Not provided",
          address: address || { street: "Not provided" },
        },
        paymentDetails: {
          method: "Card Payment",
          cardNumber: "**** **** **** ****",
          cardType: "unknown",
        },
        itemInfo: {
          title: currentOrder.content?.title || "Digital Content",
          category:
            currentOrder.content?.category ||
            currentOrder.content?.sport ||
            "Sports Content",
          image:
            currentOrder.content?.thumbnail ||
            currentOrder.content?.thumbnailUrl ||
            "https://via.placeholder.com/80x80/f0f0f0/666666?text=Content",
        },
        fullOrder: currentOrder,
        paymentResult: paymentResult,
        taxAmount: taxCalculation ? `$${(taxCalculation.amount_total / 100).toFixed(2)}` : "$0.00",
      };

      setTimeout(() => {
        navigate("/thank-you", {
          state: { orderData },
        });
      }, 200);
    }
  };

  const handlePaymentError = (error) => {
    console.error("Payment error:", error);
    toast.error(error.message || "Payment failed. Please try again.");
    setPaymentStep("error");
  };

  const handlePaymentCancel = () => {
    navigate(
      `/buyer/details/${currentOrder?.content?._id || currentOrder?.content}`
    );
  };

  const handleRetryPayment = () => {
    setPaymentStep("payment");
  };

  if (isLoading || paymentStep === "loading") {
    return <LoadingSkeleton type="checkout" />;
  }

  if (error || !currentOrder) {
    return (
      <ErrorDisplay
        title="Order Not Found"
        message={
          error ||
          "The order you're looking for doesn't exist or you don't have permission to view it."
        }
        onRetry={() => navigate("/buyer/dashboard")}
        retryText="Go to Dashboard"
      />
    );
  }

  if (currentOrder.buyer._id !== user._id && currentOrder.buyer !== user._id) {
    return (
      <ErrorDisplay
        title="Access Denied"
        message="You don't have permission to view this order."
        onRetry={() => navigate("/buyer/dashboard")}
        retryText="Go to Dashboard"
      />
    );
  }

  if (currentOrder.paymentStatus === "Completed") {
    return (
      <div className="checkout-page">
        <div className="max-container">
          <div>
            <div className="order-already-paid">
              <h2>Order Already Paid</h2>
              <p>This order has already been completed.</p>
              <button
                className="btn-primary"
                onClick={() => navigate("/buyer/downloads")}
              >
                View Downloads
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="checkout-page">
      <div className="max-container">
        <div className="checkout-content">
          {/* Left Section - Payment Form */}
          <div className="checkout-left">
            <div className="checkout-form-container">
              <h1 className="checkout-title">Complete Your Purchase</h1>

              {/* Address Input Field */}
              <div className="address-section">
                <h3>Billing Address</h3>
                <input
                  ref={inputRef}
                  type="text"
                  placeholder="Enter your address"
                  className="address-input"
                  disabled={isAddressLoading}
                />
                {isAddressLoading && <p>Calculating tax...</p>}
                {address && (
                  <p className="selected-address">
                    Selected: {address.street}, {address.city}, {address.state}{" "}
                    {address.postalCode}
                  </p>
                )}
              </div>

              {paymentStep === "loading" && (
                <div className="payment-loading">
                  <p>Loading order details...</p>
                </div>
              )}

              {paymentStep === "payment" && currentOrder ? (
                <>
                  <StripePaymentForm
                    order={currentOrder}
                    taxCalculation={taxCalculation}
                    onSuccess={handlePaymentSuccess}
                    onError={handlePaymentError}
                    onCancel={handlePaymentCancel}
                  />
                </>
              ) : (
                <div className="debug-info"></div>
              )}

              {paymentStep === "success" && (
                <div className="payment-success">
                  <div className="success-icon">✅</div>
                  <h3>Payment Successful!</h3>
                  <p>
                    Your payment has been processed successfully. Redirecting...
                  </p>
                </div>
              )}

              {paymentStep === "error" && (
                <div className="payment-error">
                  <div className="error-icon">❌</div>
                  <h3>Payment Failed</h3>
                  <p>
                    There was an issue processing your payment. Please try
                    again.
                  </p>
                  <button
                    className="btn-primary retry-btn"
                    onClick={handleRetryPayment}
                  >
                    Retry Payment
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Right Section - Order Summary */}
          <div className="checkout-right">
            <div className="order-summary">
              <h2 className="order-title">Order Summary</h2>

              <div className="rightbackgrounddiv">
                {/* Item Info */}
                <div className="item-info-section">
                  <h3 className="item-info-title">Item Details</h3>

                  <div className="item-details">
                    <div className="item-image">
                      <img
                        src={
                          currentOrder.content?.thumbnailUrl
                            ? `${IMAGE_BASE_URL}${currentOrder.content.thumbnailUrl}`
                            : "https://via.placeholder.com/80x80/f5f5f5/666666?text=IMG"
                        }
                        alt={currentOrder.content?.title || "Content"}
                        className="item-thumbnail"
                      />
                    </div>

                    <div className="item-description">
                      <h4 className="item-name">
                        {currentOrder.content?.title || "Content Title"}
                      </h4>
                      <p className="item-coach">
                        By {currentOrder.content?.coachName || "Coach"}
                      </p>
                      <p className="item-type">
                        {currentOrder.content?.contentType || "Digital Content"}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Order Info */}
                <div className="order-info-section">
                  <h3 className="order-info-title">Order Information</h3>
                  <div className="order-details">
                    <div className="order-row">
                      <span>Order ID:</span>
                      <span>#{currentOrder._id?.slice(-8).toUpperCase()}</span>
                    </div>
                    <div className="order-row">
                      <span>Order Type:</span>
                      <span>{currentOrder.orderType}</span>
                    </div>
                    <div className="order-row">
                      <span>Status:</span>
                      <span
                        className={`status ${currentOrder.status?.toLowerCase()}`}
                      >
                        {currentOrder.status}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Pricing */}
                <div className="pricing-section">
                  <div className="price-row">
                    <span className="price-label">Subtotal</span>
                    <span className="price-value">
                      ${(currentOrder.amount || 0).toFixed(2)}
                    </span>
                  </div>

                  <div className="price-row">
                    <span className="price-label">Tax</span>
                    <span className="price-value">
                      {taxCalculation
                        ? `$${(taxCalculation.amount_total / 100).toFixed(2)}`
                        : "Calculating..."}
                    </span>
                  </div>

                  {currentOrder.platformFee > 0 && (
                    <div className="price-row platform-fee-info">
                      <span className="price-label">Platform Fee (included)</span>
                      <span className="price-value">
                        ${currentOrder.platformFee?.toFixed(2)}
                      </span>
                    </div>
                  )}

                  <div className="price-row total-row">
                    <span className="price-label">Total</span>
                    <span className="price-value">
                      {taxCalculation
                        ? `$${(
                            (currentOrder.amount || 0) +
                            (taxCalculation.amount_total / 100 || 0)
                          ).toFixed(2)}`
                        : "Calculating..."}
                    </span>
                  </div>

                  {currentOrder.platformFee > 0 && (
                    <div className="fee-explanation">
                      <small>
                        You pay the listed price. Platform fee is deducted from seller earnings.
                      </small>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;